// Core exports
export * from './core/config';
export * from './core/connection-pool';
export * from './core/message';
export * from './core/handlers';
export * from './core/producer';
export * from './core/consumer';
export * from './core/serializers';

// Pattern exports
export * from './patterns';

// Middleware exports
export * from './middleware';

// Performance exports
export * from './performance';

// Utility exports
export * from './utils/logger';
export * from './utils/uuid';

// Type exports
export * from './types';

// Main RabbitMQ client class
export { RabbitMQClient } from './client';

// Version
export const VERSION = '1.0.0';
