{"name": "@roasmax/rabbitmq", "version": "1.0.0", "description": "A comprehensive rabbitmq abstraction framework for TypeScript/Node.js with enterprise-grade features", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist", "README.md", "LICENSE"], "scripts": {"build": "tsc", "build:watch": "tsc --watch", "dev": "tsx src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run clean && npm run build && npm run test", "docs:build": "typedoc src/index.ts", "example:basic": "tsx examples/basic.ts", "example:enterprise": "tsx examples/enterprise.ts", "example:performance": "tsx examples/performance.ts", "example:monitoring": "tsx examples/monitoring.ts", "example:advanced": "tsx examples/advanced.ts", "example:simple": "tsx examples/simple-test.ts", "check": "tsx scripts/check-project.ts"}, "keywords": ["rabbitmq", "message-queue", "amqp", "microservices", "async", "enterprise", "typescript", "nodejs"], "author": "roasmax", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/roasmax/rabbitmq.git"}, "bugs": {"url": "https://github.com/roasmax/rabbitmq/issues"}, "homepage": "https://github.com/roasmax/rabbitmq#readme", "engines": {"node": ">=16.0.0"}, "dependencies": {"amqplib": "^0.10.3", "ioredis": "^5.3.2", "js-yaml": "^4.1.0", "pino": "^8.16.2", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/amqplib": "^0.10.4", "@types/jest": "^29.5.8", "@types/js-yaml": "^4.0.9", "@types/node": "^20.10.0", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.1.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typedoc": "^0.25.4", "typedoc-plugin-markdown": "^4.8.0", "typescript": "^5.3.2"}, "peerDependencies": {"@opentelemetry/api": "^1.7.0", "@opentelemetry/sdk-node": "^0.45.0"}, "peerDependenciesMeta": {"@opentelemetry/api": {"optional": true}, "@opentelemetry/sdk-node": {"optional": true}}}