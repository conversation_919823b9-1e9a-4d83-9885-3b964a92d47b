{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/core/*": ["core/*"], "@/patterns/*": ["patterns/*"], "@/middleware/*": ["middleware/*"], "@/enterprise/*": ["enterprise/*"], "@/performance/*": ["performance/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}